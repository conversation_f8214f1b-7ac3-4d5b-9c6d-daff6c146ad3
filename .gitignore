# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/

# Expo
.expo/
dist/
web-build/
expo-env.d.ts

# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*.local

# typescript
*.tsbuildinfo
.vercel

# Android Studio / IntelliJ
.idea/
*.iml
*.iws
*.ipr
.gradle/
local.properties
.android/

# Android
android/app/build/
android/build/
android/.gradle/
android/captures/
android/gradlew
android/gradlew.bat
android/local.properties
*.apk
*.aab

# iOS
ios/build/
ios/Pods/
ios/*.xcworkspace
ios/*.xcuserdata

# React Native
.react-native/

# Flipper
.flipper/

# Bundle artifacts
*.jsbundle

# CocoaPods
ios/Podfile.lock

# Fastlane
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots
fastlane/test_output

# NativeWind
.nativewind/
