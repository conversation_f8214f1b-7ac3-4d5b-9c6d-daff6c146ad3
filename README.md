# Heute Du. - Inspirierende Zitate App

Eine React Native/Expo App für tägliche inspirierende Zitate, Bibelverse und Sprüche.

## 📱 Features

- **Tägliches Zitat**: Jeden Tag ein neues inspirierendes Zitat
- **Intelligente Suche**: Durchsuche Zitate nach Themen und Situationen
- **Favoriten**: Speichere deine Lieblingszitate
- **Mehrsprachig**: Deutsch und Englisch
- **AI-Integration**: Generierung neuer Zitate (optional)
- **Push-Benachrichtigungen**: Tägliche Erinnerungen
- **Premium Features**: Erweiterte Funktionen mit Abonnement

## 🚀 Erste Schritte

### Voraussetzungen

- Node.js (v18 oder höher)
- Bun oder npm/yarn
- Android Studio (für Android-Entwicklung)
- Xcode (für iOS-Entwicklung, nur macOS)
- Expo CLI

### Installation

1. **Repository klonen:**
   ```bash
   git clone https://github.com/hanno79/rork_heute_du.git
   cd rork_heute_du
   ```

2. **Dependencies installieren:**
   ```bash
   bun install
   # oder
   npm install
   ```

3. **App starten:**
   ```bash
   # Expo Development Server
   bun start
   # oder
   npm start

   # Direkt für Android
   bun android
   # oder
   npm run android
   ```

## 🔧 Android Studio Setup

### 1. Projekt in Android Studio öffnen

1. Android Studio öffnen
2. "Open an Existing Project" wählen
3. Den Projektordner auswählen
4. Warten bis das Projekt geladen ist

### 2. Expo Development Build

Für die beste Android Studio Integration:

```bash
# Expo Development Build erstellen
npx expo prebuild

# Android-spezifische Builds
npx expo run:android
```

### 3. Konfiguration prüfen

Die folgenden Konfigurationsdateien wurden automatisch erstellt:

- ✅ `babel.config.js` - Babel-Konfiguration
- ✅ `metro.config.js` - Metro Bundler-Konfiguration
- ✅ `tailwind.config.js` - TailwindCSS/NativeWind-Konfiguration
- ✅ `android/gradle.properties` - Android Gradle-Eigenschaften
- ✅ `.eslintrc.js` - ESLint-Konfiguration
- ✅ `jest.config.js` - Jest Test-Konfiguration

## 📁 Projektstruktur

```
├── app/                    # Expo Router Seiten
│   ├── (tabs)/            # Tab-Navigation
│   ├── auth/              # Authentifizierung
│   └── quote/             # Zitat-Details
├── components/            # Wiederverwendbare Komponenten
├── hooks/                 # Custom React Hooks
├── providers/             # Context Provider
├── constants/             # App-Konstanten
├── lib/                   # Utility-Funktionen
├── mocks/                 # Mock-Daten
└── assets/               # Bilder und Assets
```

## 🛠 Verfügbare Scripts

```bash
# Development
bun start                  # Expo Development Server
bun android               # Android App starten
bun ios                   # iOS App starten
bun web                   # Web Version starten

# Build
bun build                 # Production Build
bun build:android         # Android APK erstellen
bun build:ios            # iOS Build

# Testing & Linting
bun test                  # Tests ausführen
bun lint                  # Code linting
bun lint:fix             # Linting-Fehler automatisch beheben
bun typecheck            # TypeScript-Typen prüfen

# Utilities
bun clean                # Cache leeren
bun prebuild             # Native Code generieren
```

## 🎨 Styling

Die App verwendet **NativeWind** (TailwindCSS für React Native):

- Konfiguration: `tailwind.config.js`
- Globale Styles: `global.css`
- Custom Colors: `constants/colors.ts`

## 🧪 Testing

```bash
# Alle Tests ausführen
bun test

# Tests im Watch-Modus
bun test --watch

# Coverage Report
bun test --coverage
```

## 📱 App-Architektur

### Navigation
- **Expo Router** für dateibasierte Navigation
- **Tab Navigation** mit 4 Hauptbereichen
- **Stack Navigation** für Details

### State Management
- **Zustand** für globalen State
- **React Context** für Authentifizierung und Favoriten
- **AsyncStorage** für lokale Persistierung

### API Integration
- **Fetch API** für HTTP-Requests
- **AI-Integration** über Rork Toolkit (optional)
- **Offline-First** Ansatz mit lokalen Fallbacks

## 🔐 Umgebungsvariablen

Erstelle eine `.env.local` Datei:

```env
# API Configuration
EXPO_PUBLIC_API_URL=https://toolkit.rork.com
EXPO_PUBLIC_AI_ENABLED=false

# App Configuration
EXPO_PUBLIC_APP_NAME="Heute Du."
EXPO_PUBLIC_APP_VERSION=1.0.0
```

## 🚀 Deployment

### Android

1. **Development Build:**
   ```bash
   npx expo build:android
   ```

2. **Production Build:**
   ```bash
   npx expo build:android --type app-bundle
   ```

### iOS

1. **Development Build:**
   ```bash
   npx expo build:ios
   ```

2. **Production Build:**
   ```bash
   npx expo build:ios --type archive
   ```

## 🤝 Contributing

1. Fork das Repository
2. Erstelle einen Feature Branch (`git checkout -b feature/amazing-feature`)
3. Committe deine Änderungen (`git commit -m 'Add amazing feature'`)
4. Push zum Branch (`git push origin feature/amazing-feature`)
5. Öffne einen Pull Request

## 📄 Lizenz

Dieses Projekt ist unter der MIT-Lizenz lizenziert.

## 👨‍💻 Entwickler

**Hanno Rahn** - [<EMAIL>](mailto:<EMAIL>)

---

**Erstellt mit ❤️ von Rork**
