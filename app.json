{"expo": {"name": "<PERSON><PERSON>.", "slug": "heutedu", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "bundleIdentifier": "app.rork.bibel-spruch-finder", "infoPlist": {"UIBackgroundModes": ["audio"], "NSMicrophoneUsageDescription": "Allow $(PRODUCT_NAME) to access your microphone"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "app.rork.bibel-spruch-finder", "versionCode": 1, "compileSdkVersion": 34, "targetSdkVersion": 34, "buildToolsVersion": "34.0.0", "permissions": ["RECORD_AUDIO", "android.permission.RECEIVE_BOOT_COMPLETED", "android.permission.SCHEDULE_EXACT_ALARM", "android.permission.INTERNET", "android.permission.ACCESS_NETWORK_STATE", "android.permission.WAKE_LOCK", "android.permission.VIBRATE"], "blockedPermissions": ["android.permission.CAMERA", "android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_COARSE_LOCATION"], "config": {"googleMaps": {"apiKey": ""}}}, "web": {"favicon": "./assets/images/favicon.png"}, "plugins": [["expo-router", {"origin": "https://rork.com/"}], "expo-localization", ["expo-av", {"microphonePermission": "Allow $(PRODUCT_NAME) to access your microphone"}], ["expo-notifications", {"icon": "./local/assets/notification_icon.png", "color": "#ffffff", "defaultChannel": "default", "sounds": ["./local/assets/notification_sound.wav"], "enableBackgroundRemoteNotifications": false}], ["expo-audio", {"microphonePermission": "Allow $(PRODUCT_NAME) to access your microphone"}]], "experiments": {"typedRoutes": true}}}