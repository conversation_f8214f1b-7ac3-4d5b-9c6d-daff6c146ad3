module.exports = function (api) {
  api.cache(true);
  return {
    presets: [
      ['babel-preset-expo', { jsxImportSource: 'nativewind' }]
    ],
    plugins: [
      // Required for expo-router
      'expo-router/babel',
      // Required for react-native-reanimated
      'react-native-reanimated/plugin',
      // Required for NativeWind
      'nativewind/babel',
      // Required for react-native-svg
      [
        'react-native-svg-transformer',
        {
          svgOptimizerConfig: {
            plugins: [
              {
                name: 'preset-default',
                params: {
                  overrides: {
                    // disable plugins
                    removeViewBox: false,
                  },
                },
              },
            ],
          },
        },
      ],
    ],
  };
};
