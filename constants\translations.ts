export const translations = {
  en: {
    // App Name
    appName: "Heute Du.",
    
    // Navigation
    todaysQuote: "Today's Quote",
    search: "Search",
    favorites: "Favorites",
    profile: "Profile",
    
    // Home Screen
    todayQuote: "Today's Quote",
    context: "Context",
    explanation: "Explanation",
    whenToApply: "When to Apply",
    relevantFor: "This is particularly relevant when:",
    loadingQuote: "Loading today's quote...",
    
    // Search Screen
    searchQuotes: "Search Quotes",
    searchPlaceholder: "Describe your situation...",
    searchButton: "Search",
    searchingQuotes: "Searching for quotes...",
    foundQuotes: "Found {count} matching quotes",
    noQuotesFound: "No quotes found matching your search.",
    tryDifferentKeywords: "Try different keywords or a more general description.",
    findRightQuote: "Find the Right Quote",
    describeYourSituation: "Describe your situation or feelings, and we'll find relevant quotes, verses, and sayings to guide you.",
    upgradeToSearch: "Upgrade to premium to search for quotes that match your specific situation.",
    premiumFeature: "Premium Feature",
    upgradeToSearchPremium: "Upgrade to premium to search for quotes that match your situation.",
    voiceInputPremium: "Voice input is a premium feature",
    voiceInputWouldStart: "Voice input would start here in a real app",
    loadMore: "Load More",
    
    // Notifications
    notificationSettings: "Notification Settings",
    selectDays: "Select Days",
    selectTime: "Select Time",
    monday: "Monday",
    tuesday: "Tuesday",
    wednesday: "Wednesday",
    thursday: "Thursday",
    friday: "Friday",
    saturday: "Saturday",
    sunday: "Sunday",
    everyday: "Every Day",
    weekdays: "Weekdays",
    weekends: "Weekends",
    customSchedule: "Custom Schedule",
    
    // Favorites
    noFavorites: "No favorites yet",
    noFavoritesDescription: "Tap the heart icon on any quote to add it to your favorites.",
    addedToFavorites: "Added to favorites",
    removedFromFavorites: "Removed from favorites",
    
    // Profile Screen
    yourAccount: "Your Account",
    premiumStatus: "Premium Status",
    premiumActive: "Premium Active",
    freeVersion: "Free Version",
    premiumActiveDescription: "You have access to all premium features including quote search and voice input.",
    freeVersionDescription: "Upgrade to premium to unlock all features including quote search and voice input.",
    cancelPremium: "Cancel Premium",
    upgradeToPremium: "Upgrade to Premium",
    settings: "Settings",
    dailyNotifications: "Daily Notifications",
    favoriteQuotes: "Favorite Quotes",
    readingHistory: "Reading History",
    shareApp: "Share App",
    language: "Language",
    selectLanguage: "Select your preferred language",
    appVersion: "Heute Du. v1.0",
    copyright: "© 2025 Heute Du.",
    
    // Premium Screen
    premiumSubscription: "Premium Subscription",
    upgradeTitle: "Upgrade to Premium",
    upgradeSubtitle: "Unlock all features and get the most out of Heute Du.",
    monthlyPrice: "$4.99",
    period: "/month",
    yearlyPrice: "or $49.99/year (save 16%)",
    subscribeNow: "Subscribe Now",
    alreadySubscribed: "Already Subscribed",
    premiumFeatures: "Premium Features",
    situationBasedSearch: "Situation-Based Search",
    situationBasedSearchDesc: "Find the perfect quote, verse, or saying for any situation in your life",
    voiceInput: "Voice Input",
    voiceInputDesc: "Describe your situation using voice instead of typing",
    completeQuoteLibrary: "Complete Quote Library",
    completeQuoteLibraryDesc: "Access our full database of quotes, verses, sayings, and explanations",
    noAds: "No Ads",
    noAdsDesc: "Enjoy an ad-free experience throughout the app",
    freeVersionIncludes: "Free Version Includes",
    dailyQuoteWithExplanation: "Daily Quote with Explanation",
    noSituationBasedSearch: "No Situation-Based Search",
    noVoiceInput: "No Voice Input",
    subscriptionRenewal: "Subscription automatically renews unless auto-renew is turned off at least 24 hours before the end of the current period.",
    
    // Premium Banner
    unlockPremiumFeatures: "Unlock Premium Features",
    premiumBannerDescription: "Search for quotes by situation, use voice input, and access our complete library.",
    upgrade: "Upgrade",
    
    // Quote Detail
    author: "Author",
    book: "Book",
    relevantSituations: "Relevant Situations",
    tags: "Tags",
    quoteNotFound: "Quote not found",
    type: "Type",
    
    // Quote Types
    bibleVerse: "Bible Verse",
    quote: "Quote",
    saying: "Saying",
    poem: "Poem",
    
    // Common
    back: "Back",
    relevantForLabel: "Relevant for:",
    
    // Languages
    english: "English",
    german: "Deutsch",
    spanish: "Español",
    french: "Français",
  },
  de: {
    // App Name
    appName: "Heute Du.",
    
    // Navigation
    todaysQuote: "Heutiges Zitat",
    search: "Suchen",
    favorites: "Favoriten",
    profile: "Profil",
    
    // Home Screen
    todayQuote: "Heutiges Zitat",
    context: "Kontext",
    explanation: "Erklärung",
    whenToApply: "Wann anzuwenden",
    relevantFor: "Dies ist besonders relevant wenn:",
    loadingQuote: "Lade heutiges Zitat...",
    
    // Search Screen
    searchQuotes: "Zitate suchen",
    searchPlaceholder: "Beschreiben Sie Ihre Situation...",
    searchButton: "Suchen",
    searchingQuotes: "Suche nach Zitaten...",
    foundQuotes: "{count} passende Zitate gefunden",
    noQuotesFound: "Keine Zitate gefunden, die zu Ihrer Suche passen.",
    tryDifferentKeywords: "Versuchen Sie andere Suchbegriffe oder eine allgemeinere Beschreibung.",
    findRightQuote: "Das richtige Zitat finden",
    describeYourSituation: "Beschreiben Sie Ihre Situation oder Gefühle, und wir finden relevante Zitate, Verse und Sprüche für Sie.",
    upgradeToSearch: "Upgraden Sie auf Premium, um nach Zitaten zu suchen, die zu Ihrer spezifischen Situation passen.",
    premiumFeature: "Premium-Funktion",
    upgradeToSearchPremium: "Upgraden Sie auf Premium, um nach Zitaten zu suchen, die zu Ihrer Situation passen.",
    voiceInputPremium: "Spracheingabe ist eine Premium-Funktion",
    voiceInputWouldStart: "Spracheingabe würde hier in einer echten App starten",
    loadMore: "Mehr laden",
    
    // Notifications
    notificationSettings: "Benachrichtigungseinstellungen",
    selectDays: "Tage auswählen",
    selectTime: "Zeit auswählen",
    monday: "Montag",
    tuesday: "Dienstag",
    wednesday: "Mittwoch",
    thursday: "Donnerstag",
    friday: "Freitag",
    saturday: "Samstag",
    sunday: "Sonntag",
    everyday: "Jeden Tag",
    weekdays: "Wochentage",
    weekends: "Wochenende",
    customSchedule: "Benutzerdefinierter Zeitplan",
    
    // Favorites
    noFavorites: "Noch keine Favoriten",
    noFavoritesDescription: "Tippen Sie auf das Herz-Symbol bei einem Zitat, um es zu Ihren Favoriten hinzuzufügen.",
    addedToFavorites: "Zu Favoriten hinzugefügt",
    removedFromFavorites: "Aus Favoriten entfernt",
    
    // Profile Screen
    yourAccount: "Ihr Konto",
    premiumStatus: "Premium-Status",
    premiumActive: "Premium aktiv",
    freeVersion: "Kostenlose Version",
    premiumActiveDescription: "Sie haben Zugang zu allen Premium-Funktionen einschließlich Zitatsuche und Spracheingabe.",
    freeVersionDescription: "Upgraden Sie auf Premium, um alle Funktionen einschließlich Zitatsuche und Spracheingabe freizuschalten.",
    cancelPremium: "Premium kündigen",
    upgradeToPremium: "Auf Premium upgraden",
    settings: "Einstellungen",
    dailyNotifications: "Tägliche Benachrichtigungen",
    favoriteQuotes: "Lieblings-Zitate",
    readingHistory: "Lesehistorie",
    shareApp: "App teilen",
    language: "Sprache",
    selectLanguage: "Wählen Sie Ihre bevorzugte Sprache",
    appVersion: "Heute Du. v1.0",
    copyright: "© 2025 Heute Du.",
    
    // Premium Screen
    premiumSubscription: "Premium-Abonnement",
    upgradeTitle: "Auf Premium upgraden",
    upgradeSubtitle: "Schalten Sie alle Funktionen frei und holen Sie das Beste aus Heute Du. heraus",
    monthlyPrice: "4,99 €",
    period: "/Monat",
    yearlyPrice: "oder 49,99 €/Jahr (16% sparen)",
    subscribeNow: "Jetzt abonnieren",
    alreadySubscribed: "Bereits abonniert",
    premiumFeatures: "Premium-Funktionen",
    situationBasedSearch: "Situationsbasierte Suche",
    situationBasedSearchDesc: "Finden Sie das perfekte Zitat, Vers oder Spruch für jede Situation in Ihrem Leben",
    voiceInput: "Spracheingabe",
    voiceInputDesc: "Beschreiben Sie Ihre Situation mit Sprache statt zu tippen",
    completeQuoteLibrary: "Vollständige Zitat-Bibliothek",
    completeQuoteLibraryDesc: "Zugang zu unserer vollständigen Datenbank mit Zitaten, Versen, Sprüchen und Erklärungen",
    noAds: "Keine Werbung",
    noAdsDesc: "Genießen Sie eine werbefreie Erfahrung in der gesamten App",
    freeVersionIncludes: "Kostenlose Version beinhaltet",
    dailyQuoteWithExplanation: "Tägliches Zitat mit Erklärung",
    noSituationBasedSearch: "Keine situationsbasierte Suche",
    noVoiceInput: "Keine Spracheingabe",
    subscriptionRenewal: "Das Abonnement verlängert sich automatisch, es sei denn, die automatische Verlängerung wird mindestens 24 Stunden vor Ende der aktuellen Periode deaktiviert.",
    
    // Premium Banner
    unlockPremiumFeatures: "Premium-Funktionen freischalten",
    premiumBannerDescription: "Suchen Sie nach Zitaten nach Situation, verwenden Sie Spracheingabe und greifen Sie auf unsere vollständige Bibliothek zu.",
    upgrade: "Upgraden",
    
    // Quote Detail
    author: "Autor",
    book: "Buch",
    relevantSituations: "Relevante Situationen",
    tags: "Schlagwörter",
    quoteNotFound: "Zitat nicht gefunden",
    type: "Typ",
    
    // Quote Types
    bibleVerse: "Bibelvers",
    quote: "Zitat",
    saying: "Spruch",
    poem: "Gedicht",
    
    // Common
    back: "Zurück",
    relevantForLabel: "Relevant für:",
    
    // Languages
    english: "English",
    german: "Deutsch",
    spanish: "Español",
    french: "Français",
  }
};

export type TranslationKey = keyof typeof translations.en;
export type SupportedLanguage = keyof typeof translations;