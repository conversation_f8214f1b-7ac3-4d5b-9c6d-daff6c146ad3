// Temporary fix for expo-notifications asset warnings
// This file helps suppress the warnings about missing notification assets

const fs = require('fs');
const path = require('path');

// Create the local/assets directory if it doesn't exist
const assetsDir = path.join(__dirname, 'local', 'assets');
if (!fs.existsSync(assetsDir)) {
  fs.mkdirSync(assetsDir, { recursive: true });
}

// Create placeholder files to prevent warnings
const placeholderIcon = path.join(assetsDir, 'notification_icon.png');
const placeholderSound = path.join(assetsDir, 'notification_sound.wav');

// Create minimal PNG file (1x1 transparent pixel)
const pngData = Buffer.from([
  0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
  0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
  0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4, 0x89, 0x00, 0x00, 0x00,
  0x0B, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00,
  0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49,
  0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
]);

// Create minimal WAV file (silence)
const wavData = Buffer.from([
  0x52, 0x49, 0x46, 0x46, 0x24, 0x00, 0x00, 0x00, 0x57, 0x41, 0x56, 0x45,
  0x66, 0x6D, 0x74, 0x20, 0x10, 0x00, 0x00, 0x00, 0x01, 0x00, 0x01, 0x00,
  0x44, 0xAC, 0x00, 0x00, 0x88, 0x58, 0x01, 0x00, 0x02, 0x00, 0x10, 0x00,
  0x64, 0x61, 0x74, 0x61, 0x00, 0x00, 0x00, 0x00
]);

try {
  if (!fs.existsSync(placeholderIcon)) {
    fs.writeFileSync(placeholderIcon, pngData);
    console.log('Created placeholder notification icon');
  }
  
  if (!fs.existsSync(placeholderSound)) {
    fs.writeFileSync(placeholderSound, wavData);
    console.log('Created placeholder notification sound');
  }
  
  console.log('Expo notifications assets fix applied successfully');
} catch (error) {
  console.error('Failed to create placeholder assets:', error);
}

module.exports = {};