@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global styles for NativeWind */
.text-primary {
  color: #007AFF;
}

.text-secondary {
  color: #5856D6;
}

.bg-primary {
  background-color: #007AFF;
}

.bg-secondary {
  background-color: #5856D6;
}

/* Custom utility classes */
.shadow-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.border-light {
  border-color: #C6C6C8;
}

/* Typography utilities */
.text-title {
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.4;
}

.text-subtitle {
  font-size: 1.125rem;
  font-weight: 500;
  line-height: 1.5;
}

.text-body {
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.6;
}

.text-caption {
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.4;
  opacity: 0.7;
}
