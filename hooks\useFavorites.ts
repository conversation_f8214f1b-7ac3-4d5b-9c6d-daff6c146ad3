import { useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import createContextHook from '@nkzw/create-context-hook';
import { Quote } from '@/mocks/quotes';

const FAVORITES_KEY = 'favorites';

export const [FavoritesProvider, useFavorites] = createContextHook(() => {
  const [favorites, setFavorites] = useState<Quote[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    loadFavorites();
  }, []);

  const loadFavorites = async () => {
    try {
      console.log('Loading favorites from AsyncStorage...');
      const stored = await AsyncStorage.getItem(FAVORITES_KEY);
      if (stored) {
        try {
          const parsedFavorites = JSON.parse(stored);
          // Validate that the parsed data is an array
          if (Array.isArray(parsedFavorites)) {
            console.log('Loaded favorites:', parsedFavorites.length);
            setFavorites(parsedFavorites);
          } else {
            console.warn('Invalid favorites data format, resetting to empty array');
            setFavorites([]);
            // Clear corrupted data
            await AsyncStorage.removeItem(FAVORITES_KEY);
          }
        } catch (parseError) {
          console.error('Error parsing favorites data:', parseError);
          setFavorites([]);
          // Clear corrupted data
          await AsyncStorage.removeItem(FAVORITES_KEY);
        }
      } else {
        console.log('No favorites found in storage');
        setFavorites([]);
      }
    } catch (error) {
      console.error('Error loading favorites:', error);
      // Set empty array as fallback
      setFavorites([]);
    } finally {
      setIsLoading(false);
    }
  };

  const saveFavorites = async (newFavorites: Quote[]) => {
    try {
      console.log('Saving favorites to AsyncStorage:', newFavorites.length);
      
      // Validate input data
      if (!Array.isArray(newFavorites)) {
        console.error('Invalid favorites data: not an array');
        return;
      }
      
      const dataToSave = JSON.stringify(newFavorites);
      await AsyncStorage.setItem(FAVORITES_KEY, dataToSave);
      setFavorites(newFavorites);
    } catch (error) {
      console.error('Error saving favorites:', error);
      
      // If saving fails, try to maintain consistency by not updating state
      // but log the error for debugging
      if (error instanceof Error) {
        console.error('Failed to save favorites:', error.message);
      }
    }
  };

  const addToFavorites = async (quote: Quote) => {
    console.log('Adding quote to favorites:', quote.id);
    
    try {
      // Validate quote object
      if (!quote || !quote.id) {
        throw new Error('Invalid quote object: missing id');
      }

      // Check if quote is already in favorites to prevent duplicates
      const isAlreadyFavorite = favorites.some(fav => fav.id === quote.id);
      if (isAlreadyFavorite) {
        console.log('Quote is already in favorites, skipping add');
        return;
      }

      const newFavorites = [...favorites, quote];
      await saveFavorites(newFavorites);
    } catch (error) {
      console.error('Error adding to favorites:', error);
      throw error;
    }
  };

  const removeFromFavorites = async (quoteId: string) => {
    console.log('Removing quote from favorites:', quoteId);
    
    try {
      // Validate quoteId
      if (!quoteId || typeof quoteId !== 'string') {
        throw new Error('Invalid quote ID provided');
      }

      // Check if quote exists in favorites
      const quoteExists = favorites.some(fav => fav.id === quoteId);
      if (!quoteExists) {
        console.log('Quote not found in favorites, skipping remove');
        return;
      }

      const newFavorites = favorites.filter(fav => fav.id !== quoteId);
      await saveFavorites(newFavorites);
    } catch (error) {
      console.error('Error removing from favorites:', error);
      throw error;
    }
  };

  const isFavorite = (quoteId: string): boolean => {
    const result = favorites.some(fav => fav.id === quoteId);
    console.log(`Checking if quote ${quoteId} is favorite:`, result);
    return result;
  };

  const toggleFavorite = async (quote: Quote): Promise<boolean> => {
    console.log('Toggling favorite for quote:', quote.id);
    
    try {
      // Validate quote object
      if (!quote || !quote.id) {
        console.error('Invalid quote object provided to toggleFavorite');
        throw new Error('Invalid quote data');
      }

      const wasAlreadyFavorite = isFavorite(quote.id);
      
      if (wasAlreadyFavorite) {
        await removeFromFavorites(quote.id);
        return false; // Removed from favorites
      } else {
        await addToFavorites(quote);
        return true; // Added to favorites
      }
    } catch (error) {
      console.error('Error toggling favorite:', error);
      throw error; // Re-throw to let the caller handle the error
    }
  };

  return {
    favorites,
    isLoading,
    addToFavorites,
    removeFromFavorites,
    isFavorite,
    toggleFavorite,
  };
});