import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

const API_BASE_URL = process.env.EXPO_PUBLIC_RORK_API_BASE_URL || 'https://api.rork.com';
const USE_MOCK_API = true; // Set to false when backend is available

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface User {
  id: string;
  email: string;
  name: string;
  isPremium: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresAt: number;
}

class ApiClient {
  private baseUrl: string;
  private accessToken: string | null = null;

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl;
  }

  setAccessToken(token: string | null) {
    this.accessToken = token;
  }

  private generateMockUser(email: string, name: string): User {
    return {
      id: Math.random().toString(36).substr(2, 9),
      email,
      name,
      isPremium: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
  }

  private generateMockTokens(): AuthTokens {
    return {
      accessToken: 'mock_access_token_' + Math.random().toString(36).substr(2, 9),
      refreshToken: 'mock_refresh_token_' + Math.random().toString(36).substr(2, 9),
      expiresAt: Date.now() + (24 * 60 * 60 * 1000), // 24 hours
    };
  }

  private async mockDelay(ms: number = 1000): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...(options.headers as Record<string, string> || {}),
    };

    if (this.accessToken) {
      headers['Authorization'] = `Bearer ${this.accessToken}`;
    }

    // Create AbortController for timeout handling
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    try {
      const response = await fetch(url, {
        ...options,
        headers,
        signal: controller.signal,
      });

      // Clear timeout if request completes
      clearTimeout(timeoutId);

      let data;
      try {
        data = await response.json();
      } catch (parseError) {
        // Handle non-JSON responses
        data = null;
      }

      if (!response.ok) {
        return {
          success: false,
          error: data?.message || `HTTP ${response.status}: ${response.statusText}`,
        };
      }

      return {
        success: true,
        data,
      };
    } catch (error) {
      clearTimeout(timeoutId);
      console.error('API request failed:', error);
      
      // Handle different types of errors
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          return {
            success: false,
            error: 'Request timeout - please check your connection',
          };
        }
        
        // Network connectivity issues
        if (error.message.includes('fetch')) {
          return {
            success: false,
            error: 'Network error - please check your internet connection',
          };
        }
        
        return {
          success: false,
          error: error.message,
        };
      }
      
      return {
        success: false,
        error: 'Unknown network error occurred',
      };
    }
  }

  // Auth endpoints
  async login(email: string, password: string): Promise<ApiResponse<{ user: User; tokens: AuthTokens }>> {
    if (USE_MOCK_API) {
      await this.mockDelay(800);
      
      try {
        const storedUsers = await AsyncStorage.getItem('mock_users');
        const users: Array<{ email: string; password: string; user: User }> = storedUsers ? JSON.parse(storedUsers) : [];
        
        const foundUser = users.find(u => u.email === email && u.password === password);
        
        if (foundUser) {
          const tokens = this.generateMockTokens();
          return {
            success: true,
            data: {
              user: foundUser.user,
              tokens,
            },
          };
        } else {
          return {
            success: false,
            error: 'Ungültige E-Mail oder Passwort',
          };
        }
      } catch (error) {
        return {
          success: false,
          error: 'Login fehlgeschlagen',
        };
      }
    }
    
    return this.request('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });
  }

  async register(email: string, password: string, name: string): Promise<ApiResponse<{ user: User; tokens: AuthTokens }>> {
    if (USE_MOCK_API) {
      await this.mockDelay(1000);
      
      try {
        const storedUsers = await AsyncStorage.getItem('mock_users');
        const users: Array<{ email: string; password: string; user: User }> = storedUsers ? JSON.parse(storedUsers) : [];
        
        // Check if user already exists
        const existingUser = users.find(u => u.email === email);
        if (existingUser) {
          return {
            success: false,
            error: 'Ein Benutzer mit dieser E-Mail-Adresse existiert bereits',
          };
        }
        
        // Create new user
        const newUser = this.generateMockUser(email, name);
        const tokens = this.generateMockTokens();
        
        users.push({ email, password, user: newUser });
        await AsyncStorage.setItem('mock_users', JSON.stringify(users));
        
        return {
          success: true,
          data: {
            user: newUser,
            tokens,
          },
        };
      } catch (error) {
        console.error('Mock register error:', error);
        return {
          success: false,
          error: 'Registrierung fehlgeschlagen',
        };
      }
    }
    
    return this.request('/auth/register', {
      method: 'POST',
      body: JSON.stringify({ email, password, name }),
    });
  }

  async refreshToken(refreshToken: string): Promise<ApiResponse<AuthTokens>> {
    if (USE_MOCK_API) {
      await this.mockDelay(500);
      
      // In mock mode, always return new tokens
      const tokens = this.generateMockTokens();
      return {
        success: true,
        data: tokens,
      };
    }
    
    return this.request('/auth/refresh', {
      method: 'POST',
      body: JSON.stringify({ refreshToken }),
    });
  }

  async logout(): Promise<ApiResponse<void>> {
    if (USE_MOCK_API) {
      await this.mockDelay(300);
      return {
        success: true,
        data: undefined,
      };
    }
    
    return this.request('/auth/logout', {
      method: 'POST',
    });
  }

  // User endpoints
  async getProfile(): Promise<ApiResponse<User>> {
    if (USE_MOCK_API) {
      await this.mockDelay(500);
      
      // Return mock user data - in real app this would come from token
      const mockUser = this.generateMockUser('<EMAIL>', 'Mock User');
      return {
        success: true,
        data: mockUser,
      };
    }
    
    return this.request('/user/profile');
  }

  async updateProfile(data: Partial<User>): Promise<ApiResponse<User>> {
    if (USE_MOCK_API) {
      await this.mockDelay(800);
      
      // In mock mode, just return the updated data
      const mockUser = this.generateMockUser('<EMAIL>', 'Mock User');
      return {
        success: true,
        data: { ...mockUser, ...data },
      };
    }
    
    return this.request('/user/profile', {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  // Subscription endpoints
  async createSubscription(priceId: string): Promise<ApiResponse<{ clientSecret: string }>> {
    if (USE_MOCK_API) {
      await this.mockDelay(1200);
      
      return {
        success: true,
        data: {
          clientSecret: 'mock_client_secret_' + Math.random().toString(36).substr(2, 9),
        },
      };
    }
    
    return this.request('/subscriptions/create', {
      method: 'POST',
      body: JSON.stringify({ priceId }),
    });
  }

  async cancelSubscription(): Promise<ApiResponse<void>> {
    if (USE_MOCK_API) {
      await this.mockDelay(800);
      
      return {
        success: true,
        data: undefined,
      };
    }
    
    return this.request('/subscriptions/cancel', {
      method: 'POST',
    });
  }

  // Quotes endpoints
  async getFavoriteQuotes(): Promise<ApiResponse<any[]>> {
    if (USE_MOCK_API) {
      await this.mockDelay(600);
      
      try {
        const storedFavorites = await AsyncStorage.getItem('mock_favorite_quotes');
        const favorites = storedFavorites ? JSON.parse(storedFavorites) : [];
        
        return {
          success: true,
          data: favorites,
        };
      } catch (error) {
        return {
          success: false,
          error: 'Fehler beim Laden der Favoriten',
        };
      }
    }
    
    return this.request('/quotes/favorites');
  }

  async addFavoriteQuote(quoteId: string): Promise<ApiResponse<void>> {
    if (USE_MOCK_API) {
      await this.mockDelay(400);
      
      try {
        const storedFavorites = await AsyncStorage.getItem('mock_favorite_quotes');
        const favorites = storedFavorites ? JSON.parse(storedFavorites) : [];
        
        if (!favorites.includes(quoteId)) {
          favorites.push(quoteId);
          await AsyncStorage.setItem('mock_favorite_quotes', JSON.stringify(favorites));
        }
        
        return {
          success: true,
          data: undefined,
        };
      } catch (error) {
        return {
          success: false,
          error: 'Fehler beim Hinzufügen zu Favoriten',
        };
      }
    }
    
    return this.request('/quotes/favorites', {
      method: 'POST',
      body: JSON.stringify({ quoteId }),
    });
  }

  async removeFavoriteQuote(quoteId: string): Promise<ApiResponse<void>> {
    if (USE_MOCK_API) {
      await this.mockDelay(400);
      
      try {
        const storedFavorites = await AsyncStorage.getItem('mock_favorite_quotes');
        const favorites = storedFavorites ? JSON.parse(storedFavorites) : [];
        
        const updatedFavorites = favorites.filter((id: string) => id !== quoteId);
        await AsyncStorage.setItem('mock_favorite_quotes', JSON.stringify(updatedFavorites));
        
        return {
          success: true,
          data: undefined,
        };
      } catch (error) {
        return {
          success: false,
          error: 'Fehler beim Entfernen aus Favoriten',
        };
      }
    }
    
    return this.request(`/quotes/favorites/${quoteId}`, {
      method: 'DELETE',
    });
  }

  // Notifications endpoints
  async updateNotificationSettings(settings: any): Promise<ApiResponse<void>> {
    if (USE_MOCK_API) {
      await this.mockDelay(600);
      
      try {
        await AsyncStorage.setItem('mock_notification_settings', JSON.stringify(settings));
        return {
          success: true,
          data: undefined,
        };
      } catch (error) {
        return {
          success: false,
          error: 'Fehler beim Speichern der Einstellungen',
        };
      }
    }
    
    return this.request('/notifications/settings', {
      method: 'PUT',
      body: JSON.stringify(settings),
    });
  }

  async getNotificationSettings(): Promise<ApiResponse<any>> {
    if (USE_MOCK_API) {
      await this.mockDelay(500);
      
      try {
        const storedSettings = await AsyncStorage.getItem('mock_notification_settings');
        const settings = storedSettings ? JSON.parse(storedSettings) : {
          dailyQuote: true,
          motivationalReminders: false,
          weeklyDigest: true,
        };
        
        return {
          success: true,
          data: settings,
        };
      } catch (error) {
        return {
          success: false,
          error: 'Fehler beim Laden der Einstellungen',
        };
      }
    }
    
    return this.request('/notifications/settings');
  }
}

export const apiClient = new ApiClient(API_BASE_URL);
export default apiClient;