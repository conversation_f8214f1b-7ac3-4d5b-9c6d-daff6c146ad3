{"name": "expo-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "bunx rork start -p c2ssackhjr45q96pgxuf5 --tunnel", "start-web": "bunx rork start -p c2ssackhjr45q96pgxuf5 --web --tunnel", "start-web-dev": "DEBUG=expo* bunx rork start -p c2ssackhjr45q96pgxuf5 --web --tunnel"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@nkzw/create-context-hook": "^1.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/native": "^7.1.6", "@tanstack/react-query": "^5.84.0", "expo": "^53.0.4", "expo-audio": "~0.4.8", "expo-av": "~15.1.7", "expo-blur": "~14.1.4", "expo-constants": "~17.1.7", "expo-font": "~13.3.0", "expo-haptics": "~14.1.4", "expo-image": "~2.1.6", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.4", "expo-localization": "~16.1.6", "expo-location": "~18.1.4", "expo-notifications": "~0.31.4", "expo-router": "~5.0.3", "expo-splash-screen": "~0.30.7", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.6", "expo-web-browser": "~14.1.6", "lucide-react-native": "^0.475.0", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.1", "react-native-gesture-handler": "~2.24.0", "react-native-safe-area-context": "5.3.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-web": "^0.20.0", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.25.2", "@expo/ngrok": "^4.1.0", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}