import React, { useEffect } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import AsyncStorage from '@react-native-async-storage/async-storage';
import createContextHook from '@nkzw/create-context-hook';
import { apiClient, User, AuthTokens } from '@/lib/api';

interface AuthState {
  user: User | null;
  tokens: AuthTokens | null;
  isLoading: boolean;
  isAuthenticated: boolean;
}

interface AuthActions {
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  register: (email: string, password: string, name: string) => Promise<{ success: boolean; error?: string }>;
  logout: () => Promise<void>;
  refreshAuth: () => Promise<void>;
}

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 2,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

const STORAGE_KEYS = {
  USER: 'auth_user',
  TOKENS: 'auth_tokens',
} as const;

export const [AuthContext, useAuth] = createContextHook(() => {
  const [authState, setAuthState] = React.useState<AuthState>({
    user: null,
    tokens: null,
    isLoading: true,
    isAuthenticated: false,
  });

  const saveAuthData = async (user: User, tokens: AuthTokens) => {
    try {
      // Validate input data before saving
      if (!user || !user.id || !tokens || !tokens.accessToken) {
        console.error('Invalid auth data provided to saveAuthData');
        return;
      }

      // Use multiSet for atomic operation
      await AsyncStorage.multiSet([
        [STORAGE_KEYS.USER, JSON.stringify(user)],
        [STORAGE_KEYS.TOKENS, JSON.stringify(tokens)],
      ]);
      
      // Only update state after successful storage
      apiClient.setAccessToken(tokens.accessToken);
      setAuthState({
        user,
        tokens,
        isLoading: false,
        isAuthenticated: true,
      });
    } catch (error) {
      console.error('Failed to save auth data:', error);
      // If storage fails, ensure we don't have inconsistent state
      setAuthState(prev => ({ ...prev, isLoading: false }));
    }
  };

  const clearAuthData = async () => {
    try {
      // Clear API client token first to prevent authenticated requests
      apiClient.setAccessToken(null);
      
      // Use multiRemove for atomic operation
      await AsyncStorage.multiRemove([STORAGE_KEYS.USER, STORAGE_KEYS.TOKENS]);
      
      setAuthState({
        user: null,
        tokens: null,
        isLoading: false,
        isAuthenticated: false,
      });
    } catch (error) {
      console.error('Failed to clear auth data:', error);
      // Even if storage clearing fails, ensure auth state is cleared
      setAuthState({
        user: null,
        tokens: null,
        isLoading: false,
        isAuthenticated: false,
      });
    }
  };

  const login = async (email: string, password: string): Promise<{ success: boolean; error?: string }> => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true }));
      
      const response = await apiClient.login(email, password);
      
      if (response.success && response.data) {
        await saveAuthData(response.data.user, response.data.tokens);
        return { success: true };
      } else {
        setAuthState(prev => ({ ...prev, isLoading: false }));
        return { success: false, error: response.error || 'Login failed' };
      }
    } catch (error) {
      setAuthState(prev => ({ ...prev, isLoading: false }));
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Login failed' 
      };
    }
  };

  const register = async (email: string, password: string, name: string): Promise<{ success: boolean; error?: string }> => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true }));
      
      const response = await apiClient.register(email, password, name);
      
      if (response.success && response.data) {
        await saveAuthData(response.data.user, response.data.tokens);
        return { success: true };
      } else {
        setAuthState(prev => ({ ...prev, isLoading: false }));
        return { success: false, error: response.error || 'Registration failed' };
      }
    } catch (error) {
      setAuthState(prev => ({ ...prev, isLoading: false }));
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Registration failed' 
      };
    }
  };

  const logout = async () => {
    try {
      if (authState.tokens) {
        await apiClient.logout();
      }
    } catch (error) {
      console.error('Logout API call failed:', error);
    } finally {
      await clearAuthData();
    }
  };

  const refreshAuth = async () => {
    try {
      // Use multiGet for atomic operation to prevent race conditions
      const storedData = await AsyncStorage.multiGet([STORAGE_KEYS.USER, STORAGE_KEYS.TOKENS]);
      const userData = storedData[0][1];
      const tokensData = storedData[1][1];

      if (userData && tokensData) {
        let user: User;
        let tokens: AuthTokens;
        
        try {
          user = JSON.parse(userData);
          tokens = JSON.parse(tokensData);
        } catch (parseError) {
          console.error('Failed to parse stored auth data:', parseError);
          await clearAuthData();
          return;
        }

        // Validate required fields
        if (!user.id || !tokens.accessToken || !tokens.refreshToken) {
          console.error('Invalid auth data structure');
          await clearAuthData();
          return;
        }

        // Check if token is expired
        if (tokens.expiresAt > Date.now()) {
          apiClient.setAccessToken(tokens.accessToken);
          setAuthState({
            user,
            tokens,
            isLoading: false,
            isAuthenticated: true,
          });
        } else {
          // Try to refresh token
          try {
            const refreshResponse = await apiClient.refreshToken(tokens.refreshToken);
            if (refreshResponse.success && refreshResponse.data) {
              await saveAuthData(user, refreshResponse.data);
            } else {
              console.error('Token refresh failed:', refreshResponse.error);
              await clearAuthData();
            }
          } catch (refreshError) {
            console.error('Token refresh error:', refreshError);
            await clearAuthData();
          }
        }
      } else {
        setAuthState(prev => ({ ...prev, isLoading: false }));
      }
    } catch (error) {
      console.error('Failed to refresh auth:', error);
      await clearAuthData();
    }
  };

  useEffect(() => {
    refreshAuth();
  }, []);

  const actions: AuthActions = {
    login,
    register,
    logout,
    refreshAuth,
  };

  return {
    ...authState,
    ...actions,
  };
});

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthContext>
        {children}
      </AuthContext>
    </QueryClientProvider>
  );
}