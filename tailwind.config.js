/** @type {import('tailwindcss').Config} */
module.exports = {
  // NOTE: Update this to include the paths to all of your component files.
  content: [
    "./app/**/*.{js,jsx,ts,tsx}",
    "./components/**/*.{js,jsx,ts,tsx}",
    "./providers/**/*.{js,jsx,ts,tsx}",
    "./hooks/**/*.{js,jsx,ts,tsx}",
    "./constants/**/*.{js,jsx,ts,tsx}",
  ],
  presets: [require("nativewind/preset")],
  theme: {
    extend: {
      colors: {
        // Custom colors based on your constants/colors.ts
        primary: '#007AFF',
        secondary: '#5856D6',
        background: '#FFFFFF',
        card: '#F2F2F7',
        text: '#000000',
        lightText: '#8E8E93',
        border: '#C6C6C8',
        success: '#34C759',
        warning: '#FF9500',
        error: '#FF3B30',
      },
      fontFamily: {
        // Add custom fonts if needed
        'system': ['System'],
      },
      spacing: {
        // Custom spacing values
        '18': '4.5rem',
        '88': '22rem',
      },
      borderRadius: {
        // Custom border radius
        'xl': '1rem',
        '2xl': '1.5rem',
      },
      fontSize: {
        // Custom font sizes
        'xs': '0.75rem',
        'sm': '0.875rem',
        'base': '1rem',
        'lg': '1.125rem',
        'xl': '1.25rem',
        '2xl': '1.5rem',
        '3xl': '1.875rem',
        '4xl': '2.25rem',
      },
    },
  },
  plugins: [],
};
